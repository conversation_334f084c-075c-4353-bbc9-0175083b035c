import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, Dimensions } from 'react-native';
import { LineChart } from 'react-native-chart-kit';

const FundDetailsScreen = ({ route, navigation }) => {
  const { fund } = route.params;
  const [investmentAmount, setInvestmentAmount] = useState(1000);
  const [investmentType, setInvestmentType] = useState('onetime'); // 'onetime' or 'sip'
  const [paymentMethod, setPaymentMethod] = useState('upi');

  // Sample historical data for the chart
  const historicalData = {
    labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
    datasets: [
      {
        data: [
          fund.nav * 0.92,
          fund.nav * 0.95,
          fund.nav * 0.90,
          fund.nav * 0.98,
          fund.nav * 1.02,
          fund.nav
        ],
        color: (opacity = 1) => `rgba(71, 117, 234, ${opacity})`,
        strokeWidth: 2
      }
    ]
  };

  const handleBuy = () => {
    // In a real app, this would connect to a payment gateway and backend
    Alert.alert(
      "Purchase Mutual Fund",
      `Investment Details:

Fund: ${fund.name}
Current NAV: ₹${fund.nav.toFixed(2)}
Minimum Investment: ₹1,000
Your Investment: ₹${investmentAmount.toLocaleString()}
Investment Type: ${investmentType === 'onetime' ? 'One-time Investment' : 'Systematic Investment Plan (SIP)'}
Payment Method: ${paymentMethod.toUpperCase()}

Please confirm your investment details.`,
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Proceed",
          onPress: () => Alert.alert(
            "Success",
            `Your ${investmentType === 'onetime' ? 'one-time investment' : 'SIP'} of ₹${investmentAmount.toLocaleString()} in ${fund.name} has been processed successfully!`
          )
        }
      ]
    );
  };

  const handleSell = () => {
    // In a real app, this would show current holdings and sell options
    Alert.alert(
      "Sell Mutual Fund",
      "This would show your current holdings and redemption options.",
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Proceed",
          onPress: () => Alert.alert("Success", "Sell request simulation successful!")
        }
      ]
    );
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.fundName}>{fund.name}</Text>
        <Text style={styles.category}>{fund.category}</Text>
      </View>

      <View style={styles.navSection}>
        <Text style={styles.navValue}>₹{fund.nav.toFixed(2)}</Text>
        <Text style={styles.navLabel}>Net Asset Value</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Performance</Text>

        {/* Performance Chart */}
        <View style={styles.chartContainer}>
          <LineChart
            data={historicalData}
            width={Dimensions.get("window").width - 60}
            height={220}
            chartConfig={{
              backgroundColor: "#ffffff",
              backgroundGradientFrom: "#ffffff",
              backgroundGradientTo: "#ffffff",
              decimalPlaces: 2,
              color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
              labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
              style: {
                borderRadius: 16
              },
              propsForDots: {
                r: "6",
                strokeWidth: "2",
                stroke: "#4CAF50"
              }
            }}
            bezier
            style={{
              marginVertical: 8,
              borderRadius: 16
            }}
          />
          <Text style={styles.chartLabel}>6 Month Performance</Text>
        </View>

        <View style={styles.performanceRow}>
          <View style={styles.performanceItem}>
            <Text style={[
              styles.performanceValue,
              { color: fund.oneYearReturn > 0 ? 'green' : 'red' }
            ]}>
              {fund.oneYearReturn > 0 ? '+' : ''}{fund.oneYearReturn}%
            </Text>
            <Text style={styles.performanceLabel}>1 Year</Text>
          </View>
          <View style={styles.performanceItem}>
            <Text style={[
              styles.performanceValue,
              { color: fund.threeYearReturn > 0 ? 'green' : 'red' }
            ]}>
              {fund.threeYearReturn > 0 ? '+' : ''}{fund.threeYearReturn}%
            </Text>
            <Text style={styles.performanceLabel}>3 Years</Text>
          </View>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Fund Details</Text>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Risk Level</Text>
          <Text style={styles.detailValue}>{fund.riskLevel}</Text>
        </View>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Fund Category</Text>
          <Text style={styles.detailValue}>{fund.category}</Text>
        </View>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Fund ID</Text>
          <Text style={styles.detailValue}>{fund.id}</Text>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Investment Options</Text>

        {/* Investment Type Selection */}
        <View style={styles.optionSection}>
          <Text style={styles.optionLabel}>Investment Type:</Text>
          <View style={styles.optionButtonsRow}>
            <TouchableOpacity
              style={[
                styles.optionButton,
                investmentType === 'onetime' && styles.optionButtonSelected
              ]}
              onPress={() => setInvestmentType('onetime')}
            >
              <Text style={[
                styles.optionButtonText,
                investmentType === 'onetime' && styles.optionButtonTextSelected
              ]}>One-time</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.optionButton,
                investmentType === 'sip' && styles.optionButtonSelected
              ]}
              onPress={() => setInvestmentType('sip')}
            >
              <Text style={[
                styles.optionButtonText,
                investmentType === 'sip' && styles.optionButtonTextSelected
              ]}>SIP</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Investment Amount Selection */}
        <View style={styles.optionSection}>
          <Text style={styles.optionLabel}>Investment Amount:</Text>
          <View style={styles.optionButtonsRow}>
            {[1000, 5000, 10000, 25000].map((amount) => (
              <TouchableOpacity
                key={amount}
                style={[
                  styles.amountButton,
                  investmentAmount === amount && styles.optionButtonSelected
                ]}
                onPress={() => setInvestmentAmount(amount)}
              >
                <Text style={[
                  styles.optionButtonText,
                  investmentAmount === amount && styles.optionButtonTextSelected
                ]}>₹{amount.toLocaleString()}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Payment Method Selection */}
        <View style={styles.optionSection}>
          <Text style={styles.optionLabel}>Payment Method:</Text>
          <View style={styles.optionButtonsRow}>
            {['upi', 'netbanking', 'card'].map((method) => (
              <TouchableOpacity
                key={method}
                style={[
                  styles.optionButton,
                  paymentMethod === method && styles.optionButtonSelected
                ]}
                onPress={() => setPaymentMethod(method)}
              >
                <Text style={[
                  styles.optionButtonText,
                  paymentMethod === method && styles.optionButtonTextSelected
                ]}>{method === 'upi' ? 'UPI' : method === 'netbanking' ? 'Net Banking' : 'Card'}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>

      <View style={styles.actionButtons}>
        <TouchableOpacity style={[styles.button, styles.buyButton]} onPress={handleBuy}>
          <Text style={styles.buttonText}>Buy</Text>
        </TouchableOpacity>
        <TouchableOpacity style={[styles.button, styles.sellButton]} onPress={handleSell}>
          <Text style={styles.buttonText}>Sell</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  fundName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  category: {
    fontSize: 16,
    color: '#666',
  },
  navSection: {
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
    marginTop: 16,
    borderRadius: 12,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  navValue: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  navLabel: {
    fontSize: 14,
    color: '#666',
  },
  section: {
    backgroundColor: '#fff',
    marginTop: 16,
    padding: 20,
    borderRadius: 12,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  chartContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  chartLabel: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
    textAlign: 'center',
  },
  performanceRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 10,
  },
  performanceItem: {
    alignItems: 'center',
  },
  performanceValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  performanceLabel: {
    fontSize: 14,
    color: '#666',
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  detailLabel: {
    fontSize: 16,
    color: '#666',
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  optionSection: {
    marginBottom: 16,
  },
  optionLabel: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  optionButtonsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  optionButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#ddd',
    marginRight: 10,
    marginBottom: 10,
  },
  amountButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#ddd',
    marginRight: 8,
    marginBottom: 8,
  },
  optionButtonSelected: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  optionButtonText: {
    fontSize: 14,
    color: '#333',
  },
  optionButtonTextSelected: {
    color: '#fff',
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    marginTop: 16,
    marginBottom: 32,
  },
  button: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 8,
  },
  buyButton: {
    backgroundColor: '#4CAF50',
  },
  sellButton: {
    backgroundColor: '#F44336',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default FundDetailsScreen;
