import React from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, SafeAreaView } from 'react-native';
import { useRouter } from 'expo-router';

// Sample data for mutual funds
const SAMPLE_FUNDS = [
  {
    id: '1',
    name: 'Vanguard 500 Index Fund',
    category: 'Index Fund',
    nav: 380.45,
    oneYearReturn: 15.2,
    threeYearReturn: 12.8,
    riskLevel: 'Moderate',
  },
  {
    id: '2',
    name: 'Fidelity Contrafund',
    category: 'Large Growth',
    nav: 14.32,
    oneYearReturn: 18.5,
    threeYearReturn: 14.2,
    riskLevel: 'Moderate-High',
  },
  {
    id: '3',
    name: 'T. Rowe Price Blue Chip Growth',
    category: 'Large Growth',
    nav: 159.87,
    oneYearReturn: 17.8,
    threeYearReturn: 15.1,
    riskLevel: 'Moderate-High',
  },
  {
    id: '4',
    name: 'American Funds Growth Fund of America',
    category: 'Large Growth',
    nav: 65.22,
    oneYearReturn: 16.9,
    threeYearReturn: 13.5,
    riskLevel: 'Moderate',
  },
  {
    id: '5',
    name: 'Vanguard Total Bond Market Index',
    category: 'Bond Index',
    nav: 11.27,
    oneYearReturn: 4.2,
    threeYearReturn: 3.8,
    riskLevel: 'Low',
  },
];

const FundCard = ({ fund, onPress }) => {
  return (
    <TouchableOpacity style={styles.card} onPress={onPress}>
      <Text style={styles.fundName}>{fund.name}</Text>
      <Text style={styles.category}>{fund.category}</Text>
      <View style={styles.detailsRow}>
        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>NAV</Text>
          <Text style={styles.detailValue}>₹{fund.nav.toFixed(2)}</Text>
        </View>
        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>1Y Return</Text>
          <Text style={[styles.detailValue, { color: fund.oneYearReturn > 0 ? 'green' : 'red' }]}>
            {fund.oneYearReturn > 0 ? '+' : ''}{fund.oneYearReturn}%
          </Text>
        </View>
        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>Risk</Text>
          <Text style={styles.detailValue}>{fund.riskLevel}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default function HomeScreen() {
  const router = useRouter();

  const navigateToFundDetails = (fund) => {
    // In a real app, you would navigate to a dynamic route with the fund ID
    // For now, we'll just pass the fund data as params
    router.push({
      pathname: '/fund-details',
      params: {
        id: fund.id,
        name: fund.name,
        category: fund.category,
        nav: fund.nav,
        oneYearReturn: fund.oneYearReturn,
        threeYearReturn: fund.threeYearReturn,
        riskLevel: fund.riskLevel
      }
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <FlatList
        data={SAMPLE_FUNDS}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <FundCard
            fund={item}
            onPress={() => navigateToFundDetails(item)}
          />
        )}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContainer}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  listContainer: {
    padding: 16,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  fundName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  category: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  detailItem: {
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: 12,
    color: '#888',
    marginBottom: 2,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
});
