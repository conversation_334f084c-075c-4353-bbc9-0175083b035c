import React from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, SafeAreaView } from 'react-native';

// Sample portfolio data
const PORTFOLIO_DATA = [
  {
    id: '1',
    fundName: 'Vanguard 500 Index Fund',
    investedAmount: 5000,
    currentValue: 5750,
    units: 15.12,
    nav: 380.45,
    allocationPercentage: 40,
  },
  {
    id: '2',
    fundName: 'Fidelity Contrafund',
    investedAmount: 3000,
    currentValue: 3450,
    units: 240.92,
    nav: 14.32,
    allocationPercentage: 25,
  },
  {
    id: '5',
    fundName: 'Vanguard Total Bond Market Index',
    investedAmount: 2000,
    currentValue: 2080,
    units: 184.56,
    nav: 11.27,
    allocationPercentage: 15,
  },
  {
    id: '3',
    fundName: 'T. Rowe Price Blue Chip Growth',
    investedAmount: 2500,
    currentValue: 2950,
    units: 18.45,
    nav: 159.87,
    allocationPercentage: 20,
  },
];

const PortfolioItem = ({ item, onPress }) => {
  const gainLoss = item.currentValue - item.investedAmount;
  const gainLossPercentage = (gainLoss / item.investedAmount) * 100;
  
  return (
    <TouchableOpacity style={styles.portfolioCard} onPress={onPress}>
      <Text style={styles.fundName}>{item.fundName}</Text>
      <View style={styles.valueRow}>
        <View>
          <Text style={styles.valueLabel}>Current Value</Text>
          <Text style={styles.valueAmount}>${item.currentValue.toFixed(2)}</Text>
        </View>
        <View>
          <Text style={styles.valueLabel}>Invested</Text>
          <Text style={styles.valueAmount}>${item.investedAmount.toFixed(2)}</Text>
        </View>
      </View>
      <View style={styles.detailsRow}>
        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>Units</Text>
          <Text style={styles.detailValue}>{item.units.toFixed(2)}</Text>
        </View>
        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>NAV</Text>
          <Text style={styles.detailValue}>${item.nav.toFixed(2)}</Text>
        </View>
        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>Gain/Loss</Text>
          <Text style={[
            styles.detailValue, 
            { color: gainLoss >= 0 ? 'green' : 'red' }
          ]}>
            {gainLoss >= 0 ? '+' : ''}{gainLossPercentage.toFixed(2)}%
          </Text>
        </View>
      </View>
      <View style={styles.allocationBar}>
        <View 
          style={[
            styles.allocationFill, 
            { width: `${item.allocationPercentage}%` }
          ]} 
        />
        <Text style={styles.allocationText}>{item.allocationPercentage}% of portfolio</Text>
      </View>
    </TouchableOpacity>
  );
};

const PortfolioSummary = () => {
  const totalInvested = PORTFOLIO_DATA.reduce((sum, item) => sum + item.investedAmount, 0);
  const totalValue = PORTFOLIO_DATA.reduce((sum, item) => sum + item.currentValue, 0);
  const totalGainLoss = totalValue - totalInvested;
  const totalGainLossPercentage = (totalGainLoss / totalInvested) * 100;

  return (
    <View style={styles.summaryContainer}>
      <View style={styles.summaryRow}>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryLabel}>Total Value</Text>
          <Text style={styles.summaryValue}>${totalValue.toFixed(2)}</Text>
        </View>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryLabel}>Total Invested</Text>
          <Text style={styles.summaryValue}>${totalInvested.toFixed(2)}</Text>
        </View>
      </View>
      <View style={styles.gainLossContainer}>
        <Text style={styles.gainLossLabel}>Total Gain/Loss</Text>
        <Text style={[
          styles.gainLossValue, 
          { color: totalGainLoss >= 0 ? 'green' : 'red' }
        ]}>
          {totalGainLoss >= 0 ? '+' : ''}${totalGainLoss.toFixed(2)} ({totalGainLossPercentage.toFixed(2)}%)
        </Text>
      </View>
    </View>
  );
};

const PortfolioScreen = ({ navigation }) => {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>My Portfolio</Text>
      </View>
      
      <FlatList
        data={PORTFOLIO_DATA}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <PortfolioItem 
            item={item} 
            onPress={() => navigation.navigate('FundDetails', { 
              fund: {
                id: item.id,
                name: item.fundName,
                nav: item.nav,
                // Other fund details would be fetched from API in a real app
                category: 'N/A',
                oneYearReturn: 0,
                threeYearReturn: 0,
                riskLevel: 'N/A',
              }
            })}
          />
        )}
        ListHeaderComponent={<PortfolioSummary />}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContainer}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
  },
  listContainer: {
    padding: 16,
  },
  summaryContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  summaryItem: {},
  summaryLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  gainLossContainer: {
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 12,
  },
  gainLossLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  gainLossValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  portfolioCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  fundName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  valueRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  valueLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  valueAmount: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  detailItem: {},
  detailLabel: {
    fontSize: 12,
    color: '#888',
    marginBottom: 2,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  allocationBar: {
    height: 24,
    backgroundColor: '#f0f0f0',
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
  },
  allocationFill: {
    position: 'absolute',
    top: 0,
    left: 0,
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 12,
  },
  allocationText: {
    position: 'absolute',
    width: '100%',
    textAlign: 'center',
    lineHeight: 24,
    fontSize: 12,
    fontWeight: '500',
    color: '#333',
  },
});

export default PortfolioScreen;
