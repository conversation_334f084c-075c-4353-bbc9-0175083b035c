import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, Dimensions, SafeAreaView } from 'react-native';
import { useLocalSearchParams, Stack } from 'expo-router';
import { LineChart } from 'react-native-chart-kit';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';

export default function FundDetailsScreen() {
  const params = useLocalSearchParams();
  const colorScheme = useColorScheme() ?? 'light';
  const [investmentAmount, setInvestmentAmount] = useState(1000);
  const [investmentType, setInvestmentType] = useState('onetime'); // 'onetime' or 'sip'
  const [paymentMethod, setPaymentMethod] = useState('upi');

  // Create styles based on current theme
  const styles = createStyles(colorScheme);

  // Parse the parameters
  const fund = {
    id: params.id as string,
    name: params.name as string,
    category: params.category as string,
    nav: parseFloat(params.nav as string),
    oneYearReturn: parseFloat(params.oneYearReturn as string),
    threeYearReturn: parseFloat(params.threeYearReturn as string),
    riskLevel: params.riskLevel as string,
    minInvestment: 1000, // Adding minimum investment amount
    expenseRatio: 0.75, // Adding expense ratio
  };

  // Sample chart data for the fund
  const chartData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        data: [
          fund.nav * 0.9,
          fund.nav * 0.93,
          fund.nav * 0.95,
          fund.nav * 0.97,
          fund.nav * 0.99,
          fund.nav
        ],
        color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
        strokeWidth: 2
      }
    ],
    legend: ["6 Month Performance"]
  };

  const handleBuy = () => {
    // In a real app, this would connect to a payment gateway and backend
    Alert.alert(
      "Purchase Mutual Fund",
      `Investment Details:

Fund: ${fund.name}
Current NAV: ₹${fund.nav.toFixed(2)}
Minimum Investment: ₹${fund.minInvestment.toLocaleString()}
Your Investment: ₹${investmentAmount.toLocaleString()}
Investment Type: ${investmentType === 'onetime' ? 'One-time Investment' : 'Systematic Investment Plan (SIP)'}
Payment Method: ${paymentMethod.toUpperCase()}
Expense Ratio: ${fund.expenseRatio}%
Units Allotted: ${(investmentAmount / fund.nav).toFixed(3)}

Please confirm your investment details.`,
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Proceed",
          onPress: () => Alert.alert(
            "Success",
            `Your ${investmentType === 'onetime' ? 'one-time investment' : 'SIP'} of ₹${investmentAmount.toLocaleString()} in ${fund.name} has been processed successfully!

Transaction ID: ${Math.random().toString(36).substring(2, 10).toUpperCase()}
Date: ${new Date().toLocaleDateString()}
Time: ${new Date().toLocaleTimeString()}`
          )
        }
      ]
    );
  };

  const handleSell = () => {
    // In a real app, this would show current holdings and sell options
    Alert.alert(
      "Sell Mutual Fund",
      "This would show your current holdings and redemption options.",
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Proceed",
          onPress: () => Alert.alert("Success", "Sell request simulation successful!")
        }
      ]
    );
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Fund Details',
          headerTintColor: '#4CAF50',
          headerStyle: {
            backgroundColor: colorScheme === 'dark' ? '#1E1E1E' : '#fff',
          },
          headerTitleStyle: {
            color: colorScheme === 'dark' ? '#ECEDEE' : '#333',
          },
        }}
      />
      <SafeAreaView style={styles.container}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          <View style={styles.header}>
            <Text style={styles.fundName}>{fund.name}</Text>
            <Text style={styles.category}>{fund.category}</Text>
          </View>

          <View style={styles.navSection}>
            <Text style={styles.navValue}>₹{fund.nav.toFixed(2)}</Text>
            <Text style={styles.navLabel}>Net Asset Value</Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Performance</Text>

            {/* Performance Chart */}
            <View style={styles.chartContainer}>
              <LineChart
                data={chartData}
                width={Dimensions.get("window").width - 32}
                height={180}
                chartConfig={{
                  backgroundColor: colorScheme === 'dark' ? "#1E1E1E" : "#ffffff",
                  backgroundGradientFrom: colorScheme === 'dark' ? "#1E1E1E" : "#ffffff",
                  backgroundGradientTo: colorScheme === 'dark' ? "#1E1E1E" : "#ffffff",
                  decimalPlaces: 2,
                  color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
                  labelColor: (opacity = 1) => colorScheme === 'dark' ? `rgba(255, 255, 255, ${opacity})` : `rgba(0, 0, 0, ${opacity})`,
                  style: {
                    borderRadius: 16
                  },
                  propsForDots: {
                    r: "4",
                    strokeWidth: "2",
                    stroke: "#4CAF50"
                  }
                }}
                bezier
                style={{
                  marginVertical: 8,
                  borderRadius: 16
                }}
              />
            </View>

            <View style={styles.performanceRow}>
              <View style={styles.performanceItem}>
                <Text style={[
                  styles.performanceValue,
                  { color: fund.oneYearReturn > 0 ? 'green' : 'red' }
                ]}>
                  {fund.oneYearReturn > 0 ? '+' : ''}{fund.oneYearReturn}%
                </Text>
                <Text style={styles.performanceLabel}>1 Year</Text>
              </View>
              <View style={styles.performanceItem}>
                <Text style={[
                  styles.performanceValue,
                  { color: fund.threeYearReturn > 0 ? 'green' : 'red' }
                ]}>
                  {fund.threeYearReturn > 0 ? '+' : ''}{fund.threeYearReturn}%
                </Text>
                <Text style={styles.performanceLabel}>3 Years</Text>
              </View>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Fund Details</Text>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Risk Level</Text>
              <Text style={styles.detailValue}>{fund.riskLevel}</Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Fund Category</Text>
              <Text style={styles.detailValue}>{fund.category}</Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Expense Ratio</Text>
              <Text style={styles.detailValue}>{fund.expenseRatio}%</Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Minimum Investment</Text>
              <Text style={styles.detailValue}>₹{fund.minInvestment.toLocaleString()}</Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Fund ID</Text>
              <Text style={styles.detailValue}>{fund.id}</Text>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Investment Options</Text>

            {/* Investment Type Selection */}
            <View style={styles.optionSection}>
              <Text style={styles.optionLabel}>Investment Type:</Text>
              <View style={styles.optionButtonsRow}>
                <TouchableOpacity
                  style={[
                    styles.optionButton,
                    investmentType === 'onetime' && styles.optionButtonSelected
                  ]}
                  onPress={() => setInvestmentType('onetime')}
                >
                  <Text style={[
                    styles.optionButtonText,
                    investmentType === 'onetime' && styles.optionButtonTextSelected
                  ]}>One-time</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.optionButton,
                    investmentType === 'sip' && styles.optionButtonSelected
                  ]}
                  onPress={() => setInvestmentType('sip')}
                >
                  <Text style={[
                    styles.optionButtonText,
                    investmentType === 'sip' && styles.optionButtonTextSelected
                  ]}>SIP</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Investment Amount Selection */}
            <View style={styles.optionSection}>
              <Text style={styles.optionLabel}>Investment Amount:</Text>
              <View style={styles.optionButtonsRow}>
                {[1000, 5000, 10000, 25000].map((amount) => (
                  <TouchableOpacity
                    key={amount}
                    style={[
                      styles.amountButton,
                      investmentAmount === amount && styles.optionButtonSelected
                    ]}
                    onPress={() => setInvestmentAmount(amount)}
                  >
                    <Text style={[
                      styles.optionButtonText,
                      investmentAmount === amount && styles.optionButtonTextSelected
                    ]}>₹{amount.toLocaleString()}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Payment Method Selection */}
            <View style={styles.optionSection}>
              <Text style={styles.optionLabel}>Payment Method:</Text>
              <View style={styles.optionButtonsRow}>
                {['upi', 'netbanking', 'card'].map((method) => (
                  <TouchableOpacity
                    key={method}
                    style={[
                      styles.optionButton,
                      paymentMethod === method && styles.optionButtonSelected
                    ]}
                    onPress={() => setPaymentMethod(method)}
                  >
                    <Text style={[
                      styles.optionButtonText,
                      paymentMethod === method && styles.optionButtonTextSelected
                    ]}>{method === 'upi' ? 'UPI' : method === 'netbanking' ? 'Net Banking' : 'Card'}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </View>

          <View style={styles.actionButtons}>
            <TouchableOpacity style={[styles.button, styles.buyButton]} onPress={handleBuy}>
              <Text style={styles.buttonText}>Buy</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.button, styles.sellButton]} onPress={handleSell}>
              <Text style={styles.buttonText}>Sell</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </SafeAreaView>
    </>
  );
}

// Create dynamic styles based on the theme
const createStyles = (theme: 'light' | 'dark') => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme === 'dark' ? '#151718' : '#f5f5f5',
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    paddingTop: 0,
  },
  header: {
    padding: 16,
    backgroundColor: theme === 'dark' ? '#1E1E1E' : '#fff',
    borderBottomWidth: 1,
    borderBottomColor: theme === 'dark' ? '#333' : '#e0e0e0',
    marginBottom: 16,
  },
  fundName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: theme === 'dark' ? '#ECEDEE' : '#333',
    marginBottom: 4,
  },
  category: {
    fontSize: 14,
    color: theme === 'dark' ? '#9BA1A6' : '#666',
  },
  navSection: {
    alignItems: 'center',
    padding: 16,
    backgroundColor: theme === 'dark' ? '#1E1E1E' : '#fff',
    marginBottom: 16,
    borderRadius: 12,
    shadowColor: theme === 'dark' ? '#000' : '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: theme === 'dark' ? 0.2 : 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  navValue: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme === 'dark' ? '#ECEDEE' : '#333',
    marginBottom: 4,
  },
  navLabel: {
    fontSize: 14,
    color: theme === 'dark' ? '#9BA1A6' : '#666',
  },
  section: {
    backgroundColor: theme === 'dark' ? '#1E1E1E' : '#fff',
    marginBottom: 16,
    padding: 16,
    borderRadius: 12,
    shadowColor: theme === 'dark' ? '#000' : '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: theme === 'dark' ? 0.2 : 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme === 'dark' ? '#ECEDEE' : '#333',
    marginBottom: 12,
  },
  chartContainer: {
    marginBottom: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  performanceRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  performanceItem: {
    alignItems: 'center',
  },
  performanceValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  performanceLabel: {
    fontSize: 14,
    color: theme === 'dark' ? '#9BA1A6' : '#666',
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: theme === 'dark' ? '#333' : '#f0f0f0',
  },
  detailLabel: {
    fontSize: 14,
    color: theme === 'dark' ? '#9BA1A6' : '#666',
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: theme === 'dark' ? '#ECEDEE' : '#333',
  },
  // Styles for investment options
  optionSection: {
    marginBottom: 12,
  },
  optionLabel: {
    fontSize: 14,
    color: theme === 'dark' ? '#9BA1A6' : '#666',
    marginBottom: 6,
  },
  optionButtonsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  optionButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: theme === 'dark' ? '#444' : '#ddd',
    marginRight: 8,
    marginBottom: 8,
    backgroundColor: theme === 'dark' ? '#2A2A2A' : 'transparent',
  },
  optionButtonSelected: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  optionButtonText: {
    fontSize: 13,
    color: theme === 'dark' ? '#9BA1A6' : '#666',
  },
  optionButtonTextSelected: {
    color: '#fff',
    fontWeight: '500',
  },
  amountButton: {
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: theme === 'dark' ? '#444' : '#ddd',
    marginRight: 6,
    marginBottom: 6,
    backgroundColor: theme === 'dark' ? '#2A2A2A' : 'transparent',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  button: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 6,
  },
  buyButton: {
    backgroundColor: '#4CAF50',
  },
  sellButton: {
    backgroundColor: '#F44336',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
