import { SymbolView, SymbolViewProps, SymbolWeight } from 'expo-symbols';
import { StyleProp, ViewStyle } from 'react-native';

export function IconSymbol({
  name,
  size = 24,
  color,
  style,
  weight = 'regular',
}: {
  name: SymbolViewProps['name'] | string;
  size?: number;
  color: string;
  style?: StyleProp<ViewStyle>;
  weight?: SymbolWeight;
}) {
  // Use a try-catch to handle potential errors with invalid symbol names
  try {
    return (
      <SymbolView
        weight={weight}
        tintColor={color}
        resizeMode="scaleAspectFit"
        name={name as SymbolViewProps['name']}
        style={[
          {
            width: size,
            height: size,
          },
          style,
        ]}
      />
    );
  } catch (error) {
    // Fallback to a default symbol if there's an error
    console.warn(`Error rendering symbol: ${name}`, error);
    return (
      <SymbolView
        weight={weight}
        tintColor={color}
        resizeMode="scaleAspectFit"
        name="questionmark.circle"
        style={[
          {
            width: size,
            height: size,
          },
          style,
        ]}
      />
    );
  }
}
