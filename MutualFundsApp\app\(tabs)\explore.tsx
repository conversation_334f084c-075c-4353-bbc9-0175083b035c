import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  FlatList,
  SafeAreaView,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  TouchableWithoutFeedback
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { Collapsible } from '@/components/Collapsible';
import ParallaxScrollView from '@/components/ParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { LineChart } from 'react-native-chart-kit';

// Fund categories for filtering
const FUND_CATEGORIES = [
  { id: 'all', name: 'All Funds', icon: 'apps' },
  { id: 'equity', name: 'Equity', icon: 'trending-up' },
  { id: 'debt', name: 'Debt', icon: 'cash' },
  { id: 'hybrid', name: 'Hybrid', icon: 'git-merge' },
  { id: 'index', name: 'Index', icon: 'stats-chart' },
  { id: 'sectoral', name: 'Sectoral', icon: 'business' },
  { id: 'international', name: 'International', icon: 'globe' },
];

// Sample fund data with more details
const SAMPLE_FUNDS = [
  {
    id: '1',
    name: 'Vanguard 500 Index Fund',
    category: 'Index',
    categoryId: 'index',
    nav: 380.45,
    oneYearReturn: 15.2,
    threeYearReturn: 12.8,
    fiveYearReturn: 10.5,
    riskLevel: 'Moderate',
    aum: 7520, // Assets Under Management in millions
    expenseRatio: 0.04,
    fundManager: 'Vanguard Group',
    description: 'Seeks to track the performance of the S&P 500 Index, which represents 500 of the largest U.S. companies.',
    rating: 5,
    minInvestment: 3000,
    chartData: {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      datasets: [
        {
          data: [350.45, 355.20, 362.80, 370.15, 375.30, 380.45],
          color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
          strokeWidth: 2
        }
      ]
    },
  },
  {
    id: '2',
    name: 'Fidelity Contrafund',
    category: 'Equity',
    categoryId: 'equity',
    nav: 14.32,
    oneYearReturn: 18.5,
    threeYearReturn: 14.2,
    fiveYearReturn: 12.1,
    riskLevel: 'Moderate-High',
    aum: 4250,
    expenseRatio: 0.86,
    fundManager: 'Will Danoff',
    description: 'Invests in companies whose value the manager believes is not fully recognized by the public.',
    rating: 4,
    minInvestment: 2500,
    chartData: {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      datasets: [
        {
          data: [12.10, 12.85, 13.20, 13.75, 14.10, 14.32],
          color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
          strokeWidth: 2
        }
      ]
    },
  },
  {
    id: '3',
    name: 'T. Rowe Price Blue Chip Growth',
    category: 'Equity',
    categoryId: 'equity',
    nav: 159.87,
    oneYearReturn: 17.8,
    threeYearReturn: 15.1,
    fiveYearReturn: 13.2,
    riskLevel: 'Moderate-High',
    aum: 3120,
    expenseRatio: 0.69,
    fundManager: 'Larry Puglia',
    description: 'Seeks long-term capital growth by investing primarily in common stocks of large and medium-sized blue chip companies.',
    rating: 4,
    minInvestment: 2000,
    chartData: {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      datasets: [
        {
          data: [145.20, 148.75, 152.30, 155.60, 157.90, 159.87],
          color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
          strokeWidth: 2
        }
      ]
    },
  },
  {
    id: '4',
    name: 'American Funds Growth Fund of America',
    category: 'Equity',
    categoryId: 'equity',
    nav: 65.22,
    oneYearReturn: 16.9,
    threeYearReturn: 13.5,
    fiveYearReturn: 11.8,
    riskLevel: 'Moderate',
    aum: 5680,
    expenseRatio: 0.62,
    fundManager: 'American Funds',
    description: 'Invests primarily in common stocks of companies that appear to offer superior opportunities for growth of capital.',
    rating: 4,
    minInvestment: 1000,
    chartData: {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      datasets: [
        {
          data: [60.10, 61.45, 62.80, 63.95, 64.60, 65.22],
          color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
          strokeWidth: 2
        }
      ]
    },
  },
  {
    id: '5',
    name: 'Vanguard Total Bond Market Index',
    category: 'Debt',
    categoryId: 'debt',
    nav: 11.27,
    oneYearReturn: 4.2,
    threeYearReturn: 3.8,
    fiveYearReturn: 3.2,
    riskLevel: 'Low',
    aum: 4890,
    expenseRatio: 0.05,
    fundManager: 'Vanguard Group',
    description: 'Seeks to track the performance of the Bloomberg Barclays U.S. Aggregate Float Adjusted Index.',
    rating: 4,
    minInvestment: 3000,
    chartData: {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      datasets: [
        {
          data: [10.95, 11.05, 11.12, 11.18, 11.22, 11.27],
          color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
          strokeWidth: 2
        }
      ]
    },
  },
  {
    id: '6',
    name: 'Fidelity International Index Fund',
    category: 'International',
    categoryId: 'international',
    nav: 45.67,
    oneYearReturn: 12.4,
    threeYearReturn: 9.8,
    fiveYearReturn: 8.5,
    riskLevel: 'Moderate-High',
    aum: 2340,
    expenseRatio: 0.06,
    fundManager: 'Fidelity',
    description: 'Seeks to provide investment results that correspond to the total return of foreign stock markets.',
    rating: 4,
    minInvestment: 2500,
    chartData: {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      datasets: [
        {
          data: [42.30, 43.15, 43.90, 44.50, 45.10, 45.67],
          color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
          strokeWidth: 2
        }
      ]
    },
  },
];

// Fund card component
const FundCard = ({ fund, onPress }) => {
  return (
    <TouchableOpacity style={styles.card} onPress={onPress}>
      <View style={styles.cardHeader}>
        <Text style={styles.fundName}>{fund.name}</Text>
        <View style={styles.ratingContainer}>
          {[...Array(5)].map((_, i) => (
            <Ionicons
              key={i}
              name={i < fund.rating ? "star" : "star-outline"}
              size={16}
              color={i < fund.rating ? "#FFD700" : "#ccc"}
            />
          ))}
        </View>
      </View>
      <Text style={styles.category}>{fund.category}</Text>
      <Text style={styles.description} numberOfLines={2}>{fund.description}</Text>

      {/* Performance Chart */}
      <View style={styles.chartContainer}>
        <LineChart
          data={fund.chartData}
          width={Dimensions.get("window").width - 64}
          height={120}
          chartConfig={{
            backgroundColor: "#ffffff",
            backgroundGradientFrom: "#ffffff",
            backgroundGradientTo: "#ffffff",
            decimalPlaces: 2,
            color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
            labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
            style: {
              borderRadius: 16
            },
            propsForDots: {
              r: "4",
              strokeWidth: "2",
              stroke: "#4CAF50"
            }
          }}
          bezier
          style={{
            marginVertical: 8,
            borderRadius: 8
          }}
          withInnerLines={false}
          withOuterLines={false}
          withVerticalLabels={false}
          withHorizontalLabels={false}
        />
      </View>

      <View style={styles.detailsRow}>
        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>NAV</Text>
          <Text style={styles.detailValue}>₹{fund.nav.toFixed(2)}</Text>
        </View>
        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>1Y Return</Text>
          <Text style={[styles.detailValue, { color: fund.oneYearReturn > 0 ? 'green' : 'red' }]}>
            {fund.oneYearReturn > 0 ? '+' : ''}{fund.oneYearReturn}%
          </Text>
        </View>
        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>Expense</Text>
          <Text style={styles.detailValue}>{fund.expenseRatio}%</Text>
        </View>
      </View>

      <View style={styles.footer}>
        <Text style={styles.minInvestment}>Min: ₹{fund.minInvestment}</Text>
        <Text style={styles.riskLevel}>{fund.riskLevel} Risk</Text>
      </View>
    </TouchableOpacity>
  );
};

// Category filter component
const CategoryFilter = ({ categories, selectedCategory, onSelectCategory }) => {
  return (
    <View style={styles.categoryFilterContainer}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.categoryScrollContainer}
      >
        {categories.map(category => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryButton,
              selectedCategory === category.id && styles.selectedCategoryButton
            ]}
            onPress={() => onSelectCategory(category.id)}
          >
            <View style={[
              styles.categoryIconContainer,
              selectedCategory === category.id && styles.selectedCategoryIconContainer
            ]}>
              <Ionicons
                name={category.icon}
                size={24}
                color={selectedCategory === category.id ? '#fff' : '#4CAF50'}
              />
            </View>
            <Text
              style={[
                styles.categoryButtonText,
                selectedCategory === category.id && styles.selectedCategoryButtonText
              ]}
            >
              {category.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

export default function ExploreScreen() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('name'); // 'name', 'return', 'rating'

  // Function to dismiss keyboard when tapping outside the search input
  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  // Add Android-specific keyboard handling to prevent bottom navbar from being pushed up
  React.useEffect(() => {
    if (Platform.OS === 'android') {
      const keyboardDidShowListener = Keyboard.addListener(
        'keyboardDidShow',
        () => {
          // This is intentionally left empty to override default Android behavior
        }
      );
      const keyboardDidHideListener = Keyboard.addListener(
        'keyboardDidHide',
        () => {
          // This is intentionally left empty to override default Android behavior
        }
      );

      return () => {
        keyboardDidShowListener.remove();
        keyboardDidHideListener.remove();
      };
    }
  }, []);

  // Filter funds based on search query and category
  const filteredFunds = SAMPLE_FUNDS.filter(fund => {
    const matchesSearch = fund.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         fund.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || fund.categoryId === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  // Sort funds based on selected sort option
  const sortedFunds = [...filteredFunds].sort((a, b) => {
    if (sortBy === 'name') {
      return a.name.localeCompare(b.name);
    } else if (sortBy === 'return') {
      return b.oneYearReturn - a.oneYearReturn;
    } else if (sortBy === 'rating') {
      return b.rating - a.rating;
    }
    return 0;
  });

  const navigateToFundDetails = (fund) => {
    router.push({
      pathname: '/fund-details',
      params: {
        id: fund.id,
        name: fund.name,
        category: fund.category,
        nav: fund.nav,
        oneYearReturn: fund.oneYearReturn,
        threeYearReturn: fund.threeYearReturn,
        riskLevel: fund.riskLevel
      }
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.searchContainer}>
          <Ionicons name="search" size={22} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search mutual funds..."
            placeholderTextColor="#999"
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSubmitEditing={Keyboard.dismiss}
            blurOnSubmit={true}
            returnKeyType="search"
            keyboardType="default"
            autoCapitalize="none"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => {
              setSearchQuery('');
              Keyboard.dismiss();
            }}>
              <Ionicons name="close-circle" size={20} color="#666" />
            </TouchableOpacity>
          )}
        </View>

        <CategoryFilter
          categories={FUND_CATEGORIES}
          selectedCategory={selectedCategory}
          onSelectCategory={setSelectedCategory}
        />

        <View style={styles.sortContainer}>
          <Text style={styles.sortLabel}>Sort by:</Text>
          <View style={styles.sortButtonsContainer}>
            <TouchableOpacity
              style={[styles.sortButton, sortBy === 'name' && styles.selectedSortButton]}
              onPress={() => setSortBy('name')}
            >
              <Text style={[styles.sortButtonText, sortBy === 'name' && styles.selectedSortButtonText]}>Name</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.sortButton, sortBy === 'return' && styles.selectedSortButton]}
              onPress={() => setSortBy('return')}
            >
              <Text style={[styles.sortButtonText, sortBy === 'return' && styles.selectedSortButtonText]}>Return</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.sortButton, sortBy === 'rating' && styles.selectedSortButton]}
              onPress={() => setSortBy('rating')}
            >
              <Text style={[styles.sortButtonText, sortBy === 'rating' && styles.selectedSortButtonText]}>Rating</Text>
            </TouchableOpacity>
          </View>
        </View>

        <FlatList
          data={sortedFunds}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <FundCard
              fund={item}
              onPress={() => {
                Keyboard.dismiss();
                navigateToFundDetails(item);
              }}
            />
          )}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={[
            styles.listContainer,
            // Add padding at the bottom to ensure content isn't hidden behind the tab bar
            { paddingBottom: Platform.OS === 'android' ? 60 : 16 }
          ]}
          keyboardShouldPersistTaps="handled"
          onScrollBeginDrag={Keyboard.dismiss}
          onTouchStart={dismissKeyboard}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Ionicons name="search-outline" size={48} color="#ccc" />
              <Text style={styles.emptyText}>No funds found</Text>
              <Text style={styles.emptySubtext}>Try adjusting your search or filters</Text>
            </View>
          }
        />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#f0f0f0',
  },
  searchIcon: {
    marginRight: 12,
    color: '#4CAF50',
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    fontWeight: '400',
  },
  categoryFilterContainer: {
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  categoryScrollContainer: {
    paddingHorizontal: 12,
    paddingBottom: 4,
  },
  categoryButton: {
    alignItems: 'center',
    marginRight: 16,
    width: 70,
  },
  selectedCategoryButton: {
    // No background change, just the icon and text change
  },
  selectedCategoryIconContainer: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  categoryIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#f0f7f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#e0f2e0',
  },
  categoryButtonText: {
    fontSize: 11,
    color: '#666',
    textAlign: 'center',
    marginTop: 2,
  },
  selectedCategoryButtonText: {
    color: '#4CAF50',
    fontWeight: '600',
  },
  sortContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  sortLabel: {
    fontSize: 14,
    color: '#666',
    marginRight: 8,
  },
  sortButtonsContainer: {
    flexDirection: 'row',
  },
  sortButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    marginRight: 8,
  },
  selectedSortButton: {
    backgroundColor: '#e0f2e9',
  },
  sortButtonText: {
    fontSize: 14,
    color: '#666',
  },
  selectedSortButtonText: {
    color: '#4CAF50',
    fontWeight: '600',
  },
  listContainer: {
    padding: 16,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  fundName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    marginRight: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
  },
  category: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    lineHeight: 20,
  },
  chartContainer: {
    marginBottom: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  detailItem: {
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: 12,
    color: '#888',
    marginBottom: 2,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 12,
  },
  minInvestment: {
    fontSize: 14,
    color: '#666',
  },
  riskLevel: {
    fontSize: 14,
    color: '#666',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
    textAlign: 'center',
  },
});
